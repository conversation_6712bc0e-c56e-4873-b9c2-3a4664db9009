<template>
  <div class="filter-controls">
    <!-- 度数范围筛选 -->
    <div class="filter-section">
      <h4 class="filter-title">📊 度数范围筛选</h4>
      
      <div class="range-controls">
        <div class="range-input-group">
          <label class="range-label">最小度数:</label>
          <input 
            type="number" 
            class="range-input"
            v-model.number="localMinDegree"
            @change="updateDegreeRange"
            :min="0"
            :max="localMaxDegree"
          />
        </div>
        
        <div class="range-input-group">
          <label class="range-label">最大度数:</label>
          <input 
            type="number" 
            class="range-input"
            v-model.number="localMaxDegree"
            @change="updateDegreeRange"
            :min="localMinDegree"
            :max="maxPossibleDegree"
          />
        </div>
      </div>
      
      <div class="range-sliders">
        <input 
          type="range"
          class="slider"
          v-model.number="localMinDegree"
          @input="updateDegreeRange"
          :min="0"
          :max="maxPossibleDegree"
        />
        <input 
          type="range"
          class="slider"
          v-model.number="localMaxDegree"
          @input="updateDegreeRange"
          :min="0"
          :max="maxPossibleDegree"
        />
      </div>
      
      <div class="range-display">
        显示度数范围: {{ localMinDegree }} - {{ localMaxDegree }}
      </div>
    </div>
    
    <!-- 节点类型筛选 -->
    <div class="filter-section">
      <h4 class="filter-title">🏷️ 节点类型筛选</h4>
      
      <div class="type-controls">
        <div class="type-buttons">
          <button 
            class="btn btn-secondary btn-small"
            @click="selectAllTypes"
          >
            全选
          </button>
          <button 
            class="btn btn-secondary btn-small"
            @click="deselectAllTypes"
          >
            全不选
          </button>
        </div>
        
        <div class="type-list">
          <label 
            v-for="type in graphData.availableTypes" 
            :key="type"
            class="type-item"
          >
            <input 
              type="checkbox" 
              class="checkbox"
              :checked="localSelectedTypes.has(type)"
              @change="toggleType(type)"
            />
            <span class="type-name">{{ type || '未分类' }}</span>
            <span class="type-count">{{ getTypeCount(type) }}</span>
          </label>
        </div>
      </div>
    </div>
    
    <!-- 节点搜索 -->
    <div class="filter-section">
      <h4 class="filter-title">🔍 节点搜索</h4>
      
      <div class="search-controls">
        <div class="search-input-wrapper">
          <input 
            type="text" 
            class="search-input input"
            placeholder="搜索节点名称..."
            v-model="localSearchQuery"
            @input="updateSearchQuery"
          />
          <button 
            v-if="localSearchQuery"
            class="clear-search-btn"
            @click="clearSearch"
            title="清除搜索"
          >
            ✕
          </button>
        </div>
        
        <div v-if="localSearchQuery" class="search-results">
          找到 {{ searchResultCount }} 个匹配节点
        </div>
      </div>
    </div>
    
    <!-- 重置按钮 -->
    <div class="filter-section">
      <button 
        class="btn btn-warning reset-btn"
        @click="resetAllFilters"
      >
        🔄 重置所有筛选
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useGraphDataStore } from '../stores/graphData.js'
import { debounce } from 'lodash-es'

const graphData = useGraphDataStore()

// 本地状态
const localMinDegree = ref(0)
const localMaxDegree = ref(500)
const localSelectedTypes = ref(new Set())
const localSearchQuery = ref('')

/**
 * 最大可能度数
 */
const maxPossibleDegree = computed(() => {
  if (graphData.rawNodes.length === 0) return 500
  return Math.max(...graphData.rawNodes.map(node => node.degree))
})

/**
 * 搜索结果数量
 */
const searchResultCount = computed(() => {
  if (!localSearchQuery.value.trim()) return 0
  
  const query = localSearchQuery.value.toLowerCase()
  return graphData.rawNodes.filter(node => {
    const title = (node.title || '').toLowerCase()
    const description = (node.description || '').toLowerCase()
    return title.includes(query) || description.includes(query)
  }).length
})

/**
 * 获取指定类型的节点数量
 */
function getTypeCount(type) {
  return graphData.rawNodes.filter(node => node.type === type).length
}

/**
 * 更新度数范围（防抖）
 */
const updateDegreeRange = debounce(() => {
  // 确保最小值不大于最大值
  if (localMinDegree.value > localMaxDegree.value) {
    localMinDegree.value = localMaxDegree.value
  }
  
  graphData.updateDegreeRange(localMinDegree.value, localMaxDegree.value)
}, 300)

/**
 * 切换类型选择
 */
function toggleType(type) {
  if (localSelectedTypes.value.has(type)) {
    localSelectedTypes.value.delete(type)
  } else {
    localSelectedTypes.value.add(type)
  }
  
  graphData.updateSelectedTypes(Array.from(localSelectedTypes.value))
}

/**
 * 全选类型
 */
function selectAllTypes() {
  localSelectedTypes.value = new Set(graphData.availableTypes)
  graphData.updateSelectedTypes(Array.from(localSelectedTypes.value))
}

/**
 * 全不选类型
 */
function deselectAllTypes() {
  localSelectedTypes.value = new Set()
  graphData.updateSelectedTypes(Array.from(localSelectedTypes.value))
}

/**
 * 更新搜索查询（防抖）
 */
const updateSearchQuery = debounce(() => {
  graphData.updateSearchQuery(localSearchQuery.value)
}, 300)

/**
 * 清除搜索
 */
function clearSearch() {
  localSearchQuery.value = ''
  graphData.updateSearchQuery('')
}

/**
 * 重置所有筛选
 */
function resetAllFilters() {
  graphData.resetFilters()
  syncLocalState()
}

/**
 * 同步本地状态
 */
function syncLocalState() {
  localMinDegree.value = graphData.minDegree
  localMaxDegree.value = graphData.maxDegree
  localSelectedTypes.value = new Set(graphData.selectedTypes)
  localSearchQuery.value = graphData.searchQuery
}

// 监听 store 状态变化
watch(() => graphData.minDegree, (newVal) => {
  localMinDegree.value = newVal
})

watch(() => graphData.maxDegree, (newVal) => {
  localMaxDegree.value = newVal
})

watch(() => graphData.selectedTypes, (newVal) => {
  localSelectedTypes.value = new Set(newVal)
})

watch(() => graphData.searchQuery, (newVal) => {
  localSearchQuery.value = newVal
})

// 组件挂载时同步状态
onMounted(() => {
  syncLocalState()
})
</script>

<style scoped>
.filter-controls {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filter-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(78, 205, 196, 0.2);
  border-radius: 12px;
  padding: 16px;
}

.filter-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #4ecdc4;
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-controls {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.range-input-group {
  flex: 1;
}

.range-label {
  display: block;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4px;
}

.range-input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid rgba(78, 205, 196, 0.3);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 12px;
}

.range-sliders {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 8px;
}

.range-display {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
}

.type-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.type-buttons {
  display: flex;
  gap: 8px;
}

.btn-small {
  padding: 6px 12px;
  font-size: 12px;
}

.type-list {
  max-height: 200px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.type-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.type-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.type-name {
  flex: 1;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
}

.type-count {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  font-family: 'Courier New', monospace;
}

.search-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.search-input-wrapper {
  position: relative;
}

.search-input {
  width: 100%;
  padding-right: 32px;
}

.clear-search-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  font-size: 14px;
  padding: 2px;
  border-radius: 2px;
  transition: color 0.2s ease;
}

.clear-search-btn:hover {
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
}

.search-results {
  font-size: 12px;
  color: #4ecdc4;
  text-align: center;
  padding: 4px;
}

.reset-btn {
  width: 100%;
  padding: 12px;
  font-size: 14px;
  font-weight: 600;
}

/* 滚动条样式 */
.type-list::-webkit-scrollbar {
  width: 4px;
}

.type-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.type-list::-webkit-scrollbar-thumb {
  background: rgba(78, 205, 196, 0.5);
  border-radius: 2px;
}
</style>
